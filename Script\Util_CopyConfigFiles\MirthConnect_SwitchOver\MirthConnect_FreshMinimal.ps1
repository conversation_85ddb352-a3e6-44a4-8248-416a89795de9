# PowerShell script to create a fresh minimal MirthConnect Config folder
# Author: Augment Agent
# Date: 2025-07-05
# This script creates a completely new minimal Config folder and replaces the old one

#Requires -RunAsAdministrator

param(
    [string]$ConfigRoot = "..\..\..\Config\MirthConnect\CURR\Mirth Connect",
    [string]$SourceRoot = "..\..\..\MirthConnect\CURR\Mirth Connect"
)

Write-Host "=== CREATING FRESH MINIMAL MIRTH CONFIG FOLDER ===" -ForegroundColor Green
Write-Host "This script creates a completely new minimal Config folder" -ForegroundColor Yellow
Write-Host ""

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Convert to absolute paths
$ConfigRoot = (Resolve-Path $ConfigRoot).Path
$SourceRoot = (Resolve-Path $SourceRoot).Path

Write-Host "Config Root: $ConfigRoot" -ForegroundColor Cyan
Write-Host "Source Root: $SourceRoot" -ForegroundColor Cyan
Write-Host ""

# Stop MirthConnect service
Write-Host "Step 1: Stopping MirthConnect service..." -ForegroundColor Cyan
try {
    Get-Service -Name "*Mirth*" | Where-Object {$_.Status -eq "Running"} | Stop-Service -Force
    Start-Sleep -Seconds 3
    Write-Host "✅ MirthConnect service stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 2: Create a fresh minimal folder
Write-Host "`nStep 2: Creating fresh minimal Config folder..." -ForegroundColor Cyan

$NewConfigRoot = $ConfigRoot + "_NEW_MINIMAL"
$OldConfigRoot = $ConfigRoot + "_OLD_BACKUP"

# Remove new folder if it exists
if (Test-Path $NewConfigRoot) {
    Remove-Item -Path $NewConfigRoot -Recurse -Force
}

# Create new minimal structure
New-Item -ItemType Directory -Path $NewConfigRoot -Force | Out-Null

# Essential directories to create
$EssentialDirs = @("conf", "appdata", "logs")
foreach ($dir in $EssentialDirs) {
    $dirPath = Join-Path $NewConfigRoot $dir
    New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
    Write-Host "✅ Created directory: $dir" -ForegroundColor Green
}

# Step 3: Copy ONLY essential configuration files
Write-Host "`nStep 3: Copying essential configuration files..." -ForegroundColor Cyan

$EssentialFiles = @(
    "conf\mirth.properties",
    "conf\log4j2.properties", 
    "conf\dbdrivers.xml",
    "conf\mirth-cli-config.properties",
    "conf\log4j2-cli.properties",
    "appdata\keystore.jks",
    "appdata\server.id",
    "appdata\extension.properties"
)

foreach ($file in $EssentialFiles) {
    $sourcePath = Join-Path $ConfigRoot $file
    $destPath = Join-Path $NewConfigRoot $file
    
    if (Test-Path $sourcePath) {
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "✅ Copied essential file: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Essential file not found: $file" -ForegroundColor Yellow
    }
}

# Step 4: Create symbolic links for all library directories
Write-Host "`nStep 4: Creating symbolic links for library directories..." -ForegroundColor Cyan

$DirectoriesToLink = @(
    "cli-lib",
    "client-lib",
    "manager-lib", 
    "server-lib",
    "extensions",
    "webapps",
    "public_html",
    "public_api_html",
    "docs"
)

$successCount = 0
$failCount = 0

foreach ($dir in $DirectoriesToLink) {
    $sourcePath = Join-Path $SourceRoot $dir
    $destPath = Join-Path $NewConfigRoot $dir

    if (Test-Path $sourcePath) {
        try {
            # Try symbolic link first
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "✅ Created symbolic link: $dir" -ForegroundColor Green
            $successCount++
        } catch {
            try {
                # Fallback to junction point
                cmd /c "mklink /J `"$destPath`" `"$sourcePath`"" 2>$null | Out-Null
                if (Test-Path $destPath) {
                    Write-Host "✅ Created junction point: $dir" -ForegroundColor Green
                    $successCount++
                } else {
                    throw "Junction creation failed"
                }
            } catch {
                Write-Host "❌ Failed to create link for: $dir" -ForegroundColor Red
                $failCount++
            }
        }
    } else {
        Write-Host "⚠️  Source directory not found: $dir" -ForegroundColor Yellow
        $failCount++
    }
}

# Step 5: Create symbolic links for main JAR files
Write-Host "`nStep 5: Creating symbolic links for main JAR files..." -ForegroundColor Cyan

$MainJars = @(
    "mirth-server-launcher.jar",
    "mirth-cli-launcher.jar", 
    "mirth-manager-launcher.jar"
)

foreach ($jar in $MainJars) {
    $sourcePath = Join-Path $SourceRoot $jar
    $destPath = Join-Path $NewConfigRoot $jar

    if (Test-Path $sourcePath) {
        try {
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "✅ Created symbolic link for JAR: $jar" -ForegroundColor Green
            $successCount++
        } catch {
            # Fallback to copying if symbolic link fails
            Copy-Item -Path $sourcePath -Destination $destPath -Force
            Write-Host "⚠️  Copied (link failed): $jar" -ForegroundColor Yellow
        }
    }
}

# Step 6: Replace old Config folder with new minimal one
Write-Host "`nStep 6: Replacing old Config folder with new minimal one..." -ForegroundColor Cyan

try {
    # Rename old folder as backup
    if (Test-Path $OldConfigRoot) {
        Remove-Item -Path $OldConfigRoot -Recurse -Force
    }
    Rename-Item -Path $ConfigRoot -NewName (Split-Path $OldConfigRoot -Leaf) -Force
    Write-Host "✅ Backed up old Config folder" -ForegroundColor Green
    
    # Rename new folder to replace old one
    Rename-Item -Path $NewConfigRoot -NewName (Split-Path $ConfigRoot -Leaf) -Force
    Write-Host "✅ Replaced with new minimal Config folder" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to replace Config folder: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Manual intervention required" -ForegroundColor Yellow
    exit 1
}

# Step 7: Start MirthConnect service
Write-Host "`nStep 7: Starting MirthConnect service..." -ForegroundColor Cyan
try {
    Get-Service -Name "*Mirth*" | Where-Object {$_.Status -eq "Stopped"} | Start-Service
    Write-Host "✅ MirthConnect service started" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: Could not start service: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "You may need to start it manually" -ForegroundColor Yellow
}

Write-Host "`n=== FRESH MINIMAL CONFIG CREATED! ===" -ForegroundColor Green
Write-Host "✅ Successfully created: $successCount symbolic links/junctions" -ForegroundColor Green
Write-Host "❌ Failed: $failCount items" -ForegroundColor Red
Write-Host ""
Write-Host "🎉 MASSIVE SPACE SAVINGS ACHIEVED!" -ForegroundColor Magenta
Write-Host "Config folder now contains only essential files + symbolic links" -ForegroundColor Magenta
Write-Host ""
Write-Host "📁 New Config folder structure:" -ForegroundColor Cyan
Write-Host "   • Essential configuration files only (~20 files)" -ForegroundColor White
Write-Host "   • Symbolic links to original libraries and extensions" -ForegroundColor White
Write-Host "   • Old folder backed up as: $(Split-Path $OldConfigRoot -Leaf)" -ForegroundColor White
Write-Host ""
Write-Host "Configuration location: $ConfigRoot" -ForegroundColor Yellow
Write-Host ""
Write-Host "To restore old folder if needed:" -ForegroundColor Yellow
Write-Host "1. Stop MirthConnect service" -ForegroundColor White
Write-Host "2. Rename current folder to _temp" -ForegroundColor White
Write-Host "3. Rename backup folder back to original name" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
