{"folders": [{"path": "../../.."}], "settings": {"sqltools.connections": [{"previewLimit": 50, "server": "localhost", "port": 5432, "askForPassword": true, "driver": "PostgreSQL", "name": "Test", "database": "test_db", "username": "root"}], "workbench.colorCustomizations": {"activityBar.activeBackground": "#6a6684", "activityBar.background": "#6a6684", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#602820", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#6a6684", "statusBar.background": "#535067", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#6a6684", "statusBarItem.remoteBackground": "#535067", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#535067", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#53506799", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#535067"}}