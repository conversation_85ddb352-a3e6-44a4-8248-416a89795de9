# PowerShell script to set up necessary directories and files for MirthConnect to run from Config folder
# Author: Augment Agent
# Date: 2025-07-05
# Updated: 2025-07-05 - Added missing libraries and extension JAR files

param(
    [string]$ConfigRoot = "..\..\..\Config\MirthConnect\CURR\Mirth Connect",
    [string]$SourceRoot = "..\..\..\MirthConnect\CURR\Mirth Connect"
)

Write-Host "Setting up MirthConnect directories and files in Config folder..." -ForegroundColor Green
Write-Host "Config Root: $ConfigRoot" -ForegroundColor Yellow
Write-Host "Source Root: $SourceRoot" -ForegroundColor Yellow

# Define required directories that MirthConnect needs
$RequiredDirectories = @(
    "logs",
    "webapps",
    "public_html",
    "public_api_html",
    "cli-lib",
    "client-lib",
    "manager-lib",
    "server-lib",
    "docs"
)

Write-Host "`nCreating required directories..." -ForegroundColor Cyan

# Create required directories if they don't exist
foreach ($dir in $RequiredDirectories) {
    $dirPath = Join-Path $ConfigRoot $dir
    if (!(Test-Path $dirPath)) {
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory already exists: $dir" -ForegroundColor Gray
    }
}

# Copy essential library directories from the original installation
Write-Host "`nCopying essential library directories..." -ForegroundColor Cyan

$LibraryDirectories = @(
    "cli-lib",
    "client-lib",
    "manager-lib",
    "server-lib",
    "docs"
)

foreach ($libDir in $LibraryDirectories) {
    $sourcePath = Join-Path $SourceRoot $libDir
    $destPath = Join-Path $ConfigRoot $libDir

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
        Write-Host "Copied library directory: $libDir" -ForegroundColor Green
    } elseif (Test-Path $destPath) {
        Write-Host "Library directory already exists: $libDir" -ForegroundColor Gray
    } else {
        Write-Host "Source library directory not found: $libDir" -ForegroundColor Yellow
    }
}

# Copy main JAR files
Write-Host "`nCopying main JAR files..." -ForegroundColor Cyan

$jarFiles = Get-ChildItem -Path $SourceRoot -Filter "*.jar" -File
foreach ($jar in $jarFiles) {
    $destPath = Join-Path $ConfigRoot $jar.Name
    if (!(Test-Path $destPath)) {
        Copy-Item -Path $jar.FullName -Destination $destPath -Force
        Write-Host "Copied JAR file: $($jar.Name)" -ForegroundColor Green
    } else {
        Write-Host "JAR file already exists: $($jar.Name)" -ForegroundColor Gray
    }
}

# Copy extension JAR files (critical for MirthConnect functionality)
Write-Host "`nCopying extension JAR files..." -ForegroundColor Cyan

$extensionsSource = Join-Path $SourceRoot "extensions"
$extensionsDest = Join-Path $ConfigRoot "extensions"

if (Test-Path $extensionsSource) {
    $extensionDirs = Get-ChildItem -Path $extensionsSource -Directory
    foreach ($extDir in $extensionDirs) {
        $sourceExtPath = $extDir.FullName
        $destExtPath = Join-Path $extensionsDest $extDir.Name

        # Copy the entire extension directory to ensure all JAR files are included
        Copy-Item -Path $sourceExtPath -Destination $extensionsDest -Recurse -Force
        Write-Host "Copied extension: $($extDir.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "Extensions source directory not found" -ForegroundColor Yellow
}

# Copy essential web files
Write-Host "`nCopying essential web files..." -ForegroundColor Cyan

$FilesToCopy = @(
    "webapps\webadmin.war",
    "public_html\index.html",
    "public_api_html\index.html"
)

foreach ($file in $FilesToCopy) {
    $sourcePath = Join-Path $SourceRoot $file
    $destPath = Join-Path $ConfigRoot $file

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        $destDir = Split-Path $destPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "Copied essential file: $file" -ForegroundColor Green
    } elseif (Test-Path $destPath) {
        Write-Host "File already exists: $file" -ForegroundColor Gray
    } else {
        Write-Host "Source file not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "`nMirthConnect Config directory setup completed!" -ForegroundColor Green
Write-Host "MirthConnect should now be able to run using configuration from: $ConfigRoot" -ForegroundColor Yellow
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Ensure VM options files have been updated with -Duser.dir parameter" -ForegroundColor White
Write-Host "2. Restart MirthConnect service to use the Config folder" -ForegroundColor White
Write-Host "3. Verify web interface is accessible at http://localhost:8081/" -ForegroundColor White
