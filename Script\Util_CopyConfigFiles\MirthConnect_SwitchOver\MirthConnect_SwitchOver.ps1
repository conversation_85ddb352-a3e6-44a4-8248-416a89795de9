# PowerShell script to set up ESSENTIAL directories and files for MirthConnect to run from Config folder
# Author: Augment Agent
# Date: 2025-07-05
# Updated: 2025-07-05 - Optimized to copy only essential files, reducing duplication

param(
    [string]$ConfigRoot = "..\..\..\Config\MirthConnect\CURR\Mirth Connect",
    [string]$SourceRoot = "..\..\..\MirthConnect\CURR\Mirth Connect"
)

Write-Host "Setting up ESSENTIAL MirthConnect files in Config folder (minimal duplication)..." -ForegroundColor Green
Write-Host "Config Root: $ConfigRoot" -ForegroundColor Yellow
Write-Host "Source Root: $SourceRoot" -ForegroundColor Yellow

# Define ESSENTIAL directories that MirthConnect absolutely needs to run
# Note: We're using symbolic links for large library directories to avoid duplication
$RequiredDirectories = @(
    "logs",
    "webapps"
)

Write-Host "`nCreating required directories..." -ForegroundColor Cyan

# Create required directories if they don't exist
foreach ($dir in $RequiredDirectories) {
    $dirPath = Join-Path $ConfigRoot $dir
    if (!(Test-Path $dirPath)) {
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory already exists: $dir" -ForegroundColor Gray
    }
}

# Create symbolic links for library directories to avoid duplication
Write-Host "`nCreating symbolic links for library directories (avoids copying hundreds of JAR files)..." -ForegroundColor Cyan

$LibraryDirectories = @(
    "cli-lib",
    "client-lib",
    "manager-lib",
    "server-lib"
)

foreach ($libDir in $LibraryDirectories) {
    $sourcePath = Join-Path $SourceRoot $libDir
    $destPath = Join-Path $ConfigRoot $libDir

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        try {
            # Create symbolic link instead of copying to save space
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "Created symbolic link for library directory: $libDir" -ForegroundColor Green
        } catch {
            # Fallback to copying if symbolic link fails (requires admin rights)
            Write-Host "Symbolic link failed (admin rights required), copying instead: $libDir" -ForegroundColor Yellow
            Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
            Write-Host "Copied library directory: $libDir" -ForegroundColor Green
        }
    } elseif (Test-Path $destPath) {
        Write-Host "Library directory already exists: $libDir" -ForegroundColor Gray
    } else {
        Write-Host "Source library directory not found: $libDir" -ForegroundColor Yellow
    }
}

# Copy main JAR files
Write-Host "`nCopying main JAR files..." -ForegroundColor Cyan

$jarFiles = Get-ChildItem -Path $SourceRoot -Filter "*.jar" -File
foreach ($jar in $jarFiles) {
    $destPath = Join-Path $ConfigRoot $jar.Name
    if (!(Test-Path $destPath)) {
        Copy-Item -Path $jar.FullName -Destination $destPath -Force
        Write-Host "Copied JAR file: $($jar.Name)" -ForegroundColor Green
    } else {
        Write-Host "JAR file already exists: $($jar.Name)" -ForegroundColor Gray
    }
}

# Create symbolic link for extensions directory to avoid duplicating all extension JAR files
Write-Host "`nCreating symbolic link for extensions directory..." -ForegroundColor Cyan

$extensionsSource = Join-Path $SourceRoot "extensions"
$extensionsDest = Join-Path $ConfigRoot "extensions"

if ((Test-Path $extensionsSource) -and !(Test-Path $extensionsDest)) {
    try {
        # Create symbolic link for entire extensions directory
        New-Item -ItemType SymbolicLink -Path $extensionsDest -Target $extensionsSource -Force | Out-Null
        Write-Host "Created symbolic link for extensions directory (saves ~100+ JAR files)" -ForegroundColor Green
    } catch {
        # Fallback to copying if symbolic link fails
        Write-Host "Symbolic link failed, copying extensions directory instead..." -ForegroundColor Yellow
        Copy-Item -Path $extensionsSource -Destination $extensionsDest -Recurse -Force
        Write-Host "Copied extensions directory" -ForegroundColor Green
    }
} elseif (Test-Path $extensionsDest) {
    Write-Host "Extensions directory already exists" -ForegroundColor Gray
} else {
    Write-Host "Extensions source directory not found" -ForegroundColor Yellow
}

# Copy only the essential web files (minimal approach)
Write-Host "`nCopying essential web files..." -ForegroundColor Cyan

$FilesToCopy = @(
    "webapps\webadmin.war"
)

# Create symbolic links for public HTML directories instead of copying
$DirectoriesToLink = @(
    "public_html",
    "public_api_html"
)

foreach ($file in $FilesToCopy) {
    $sourcePath = Join-Path $SourceRoot $file
    $destPath = Join-Path $ConfigRoot $file

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        $destDir = Split-Path $destPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "Copied essential file: $file" -ForegroundColor Green
    } elseif (Test-Path $destPath) {
        Write-Host "File already exists: $file" -ForegroundColor Gray
    } else {
        Write-Host "Source file not found: $file" -ForegroundColor Yellow
    }
}

# Create symbolic links for public HTML directories
Write-Host "`nCreating symbolic links for public HTML directories..." -ForegroundColor Cyan

foreach ($dir in $DirectoriesToLink) {
    $sourcePath = Join-Path $SourceRoot $dir
    $destPath = Join-Path $ConfigRoot $dir

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        try {
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "Created symbolic link for directory: $dir" -ForegroundColor Green
        } catch {
            Write-Host "Symbolic link failed, copying directory: $dir" -ForegroundColor Yellow
            Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
            Write-Host "Copied directory: $dir" -ForegroundColor Green
        }
    } elseif (Test-Path $destPath) {
        Write-Host "Directory already exists: $dir" -ForegroundColor Gray
    } else {
        Write-Host "Source directory not found: $dir" -ForegroundColor Yellow
    }
}

Write-Host "`nOptimized MirthConnect Config directory setup completed!" -ForegroundColor Green
Write-Host "SPACE SAVED: Using symbolic links instead of copying hundreds of JAR files" -ForegroundColor Magenta
Write-Host "MirthConnect should now be able to run using configuration from: $ConfigRoot" -ForegroundColor Yellow
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Ensure VM options files have been updated with -Duser.dir parameter" -ForegroundColor White
Write-Host "2. Restart MirthConnect service to use the Config folder" -ForegroundColor White
Write-Host "3. Verify web interface is accessible at http://localhost:8081/" -ForegroundColor White
Write-Host "`nNote: If symbolic links don't work (admin rights required), the script falls back to copying." -ForegroundColor Yellow
