# PowerShell script to create junction points for MirthConnect directories
# Author: Augment Agent
# Date: 2025-07-05
# This script uses junction points instead of symbolic links (may work better)

#Requires -RunAsAdministrator

param(
    [string]$ConfigRoot = "..\..\..\Config\MirthConnect\CURR\Mirth Connect",
    [string]$SourceRoot = "..\..\..\MirthConnect\CURR\Mirth Connect"
)

Write-Host "=== MIRTH CLEANUP USING JUNCTION POINTS ===" -ForegroundColor Green
Write-Host "Junction points work differently than symbolic links and may be more successful" -ForegroundColor Yellow
Write-Host ""

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    exit 1
}

# Convert to absolute paths
$ConfigRoot = (Resolve-Path $ConfigRoot).Path
$SourceRoot = (Resolve-Path $SourceRoot).Path

Write-Host "Config Root: $ConfigRoot" -ForegroundColor Cyan
Write-Host "Source Root: $SourceRoot" -ForegroundColor Cyan
Write-Host ""

# Function to create junction point using mklink
function New-JunctionPoint {
    param(
        [string]$LinkPath,
        [string]$TargetPath,
        [string]$Name
    )
    
    try {
        # Use cmd mklink to create junction
        $result = cmd /c "mklink /J `"$LinkPath`" `"$TargetPath`"" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Created junction: $Name" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to create junction: $Name - $result" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Exception creating junction: $Name - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to force delete directory using multiple methods
function Remove-DirectoryForce {
    param([string]$Path, [string]$Name)
    
    if (!(Test-Path $Path)) {
        Write-Host "✅ Directory doesn't exist: $Name" -ForegroundColor Gray
        return $true
    }
    
    Write-Host "Attempting to remove: $Name" -ForegroundColor Yellow
    
    # Method 1: Standard PowerShell removal
    try {
        Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
        Write-Host "✅ Removed using PowerShell: $Name" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "⚠️  PowerShell removal failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    # Method 2: Use robocopy to empty the directory
    try {
        $tempEmpty = Join-Path $env:TEMP "empty_$(Get-Random)"
        New-Item -ItemType Directory -Path $tempEmpty -Force | Out-Null
        
        # Mirror empty directory to target (deletes all content)
        $robocopyResult = robocopy $tempEmpty $Path /MIR /R:0 /W:0 /NFL /NDL /NJH /NJS
        Remove-Item -Path $Path -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $tempEmpty -Force -ErrorAction SilentlyContinue
        
        if (!(Test-Path $Path)) {
            Write-Host "✅ Removed using robocopy: $Name" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️  Robocopy removal failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    # Method 3: Use rd command
    try {
        cmd /c "rd /s /q `"$Path`"" 2>$null
        if (!(Test-Path $Path)) {
            Write-Host "✅ Removed using rd command: $Name" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️  rd command failed" -ForegroundColor Yellow
    }
    
    Write-Host "❌ Could not remove directory: $Name" -ForegroundColor Red
    return $false
}

# Stop MirthConnect service
Write-Host "Ensuring MirthConnect is stopped..." -ForegroundColor Cyan
try {
    Get-Service -Name "*Mirth*" | Where-Object {$_.Status -eq "Running"} | Stop-Service -Force
    Start-Sleep -Seconds 3
    Write-Host "✅ MirthConnect services stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Directories to process
$DirectoriesToProcess = @(
    @{Name="cli-lib"; Exists=$false},
    @{Name="client-lib"; Exists=$false},
    @{Name="manager-lib"; Exists=$false},
    @{Name="public_html"; Exists=$false},
    @{Name="public_api_html"; Exists=$false},
    @{Name="docs"; Exists=$false}
)

# Check which directories don't exist (these we can create junctions for)
foreach ($dir in $DirectoriesToProcess) {
    $configPath = Join-Path $ConfigRoot $dir.Name
    $dir.Exists = Test-Path $configPath
    
    if ($dir.Exists) {
        Write-Host "Directory exists: $($dir.Name)" -ForegroundColor Yellow
    } else {
        Write-Host "Directory missing: $($dir.Name) (can create junction)" -ForegroundColor Green
    }
}

# Create junction points for missing directories
Write-Host "`nCreating junction points for missing directories..." -ForegroundColor Cyan

$successCount = 0
$failCount = 0

foreach ($dir in $DirectoriesToProcess) {
    if (!$dir.Exists) {
        $sourcePath = Join-Path $SourceRoot $dir.Name
        $destPath = Join-Path $ConfigRoot $dir.Name
        
        if (Test-Path $sourcePath) {
            $success = New-JunctionPoint -LinkPath $destPath -TargetPath $sourcePath -Name $dir.Name
            if ($success) {
                $successCount++
            } else {
                $failCount++
            }
        } else {
            Write-Host "⚠️  Source directory not found: $($dir.Name)" -ForegroundColor Yellow
            $failCount++
        }
    }
}

# Try to remove and recreate existing directories as junctions
Write-Host "`nAttempting to replace existing directories with junctions..." -ForegroundColor Cyan

$DirectoriesToReplace = @("server-lib", "extensions", "webapps")

foreach ($dirName in $DirectoriesToReplace) {
    $configPath = Join-Path $ConfigRoot $dirName
    $sourcePath = Join-Path $SourceRoot $dirName
    
    if ((Test-Path $configPath) -and (Test-Path $sourcePath)) {
        Write-Host "`nProcessing: $dirName" -ForegroundColor White
        
        # Try to remove the existing directory
        $removed = Remove-DirectoryForce -Path $configPath -Name $dirName
        
        if ($removed) {
            # Create junction point
            $success = New-JunctionPoint -LinkPath $configPath -TargetPath $sourcePath -Name $dirName
            if ($success) {
                $successCount++
            } else {
                $failCount++
                # If junction failed, copy back the directory
                Write-Host "⚠️  Junction failed, restoring directory..." -ForegroundColor Yellow
                try {
                    Copy-Item -Path $sourcePath -Destination $configPath -Recurse -Force
                    Write-Host "✅ Directory restored: $dirName" -ForegroundColor Green
                } catch {
                    Write-Host "❌ Could not restore directory: $dirName" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "⚠️  Could not remove existing directory: $dirName" -ForegroundColor Yellow
            $failCount++
        }
    }
}

# Start MirthConnect service
Write-Host "`nStarting MirthConnect service..." -ForegroundColor Cyan
try {
    Get-Service -Name "*Mirth*" | Where-Object {$_.Status -eq "Stopped"} | Start-Service
    Write-Host "✅ MirthConnect service started" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: Could not start service: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n=== JUNCTION POINT CLEANUP RESULTS ===" -ForegroundColor Green
Write-Host "✅ Successfully created: $successCount junction points" -ForegroundColor Green
Write-Host "❌ Failed: $failCount items" -ForegroundColor Red

if ($successCount -gt 0) {
    Write-Host "`n🎉 SUCCESS: Created $successCount junction points!" -ForegroundColor Green
    Write-Host "These directories now point to original files instead of duplicating them" -ForegroundColor Magenta
}

Write-Host "`nConfiguration location: $ConfigRoot" -ForegroundColor Cyan
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
