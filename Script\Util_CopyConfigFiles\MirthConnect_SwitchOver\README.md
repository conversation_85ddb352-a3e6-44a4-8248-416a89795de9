# MirthConnect Configuration Switchover Guide

## Overview

This comprehensive guide explains how to configure MirthConnect to load its configuration from the centralized Config folder (`C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect`) instead of the default installation directory (`C:\Mobile Aspects\MirthConnect\CURR\Mirth Connect`).

## Why Switch to Config Folder?

1. **Centralized Configuration Management**: All configuration files in one location
2. **Version Control**: Easy to track configuration changes
3. **Backup and Restore**: Simplified backup of all settings
4. **Environment Management**: Different environments can have separate Config folders
5. **Deployment**: Configuration changes can be deployed independently of the application

## Files in This Directory

### `MirthConnect_SwitchOver.ps1`
PowerShell script that:
- Creates symbolic links for library directories (cli-lib, client-lib, manager-lib, server-lib)
- Creates symbolic links for extensions directory
- Copies only essential files (main JARs, webadmin.war)
- Uses relative paths for portability
- Reduces file duplication while ensuring MirthConnect can run from Config folder

### `MirthConnect_Minimal.ps1` (RECOMMENDED)
Ultra-minimal PowerShell script that:
- Copies ONLY essential configuration files (*.properties, keystore, etc.)
- Creates symbolic links for ALL other directories and files
- Provides maximum space efficiency (saves 500MB+ of duplicated files)
- Requires Administrator privileges for symbolic links (falls back gracefully)
- Recommended approach for minimal duplication

### `README.md`
This comprehensive documentation file that explains:
- Complete switchover process with multiple setup options
- Space-efficient approaches using symbolic links
- Required files and directory structure
- Verification steps
- Troubleshooting guide
- How to switch back to original folder

## Space Efficiency Comparison

| Approach | Files Copied | Space Used | Symbolic Links | Admin Required |
|----------|-------------|------------|----------------|----------------|
| **Minimal (Recommended)** | ~20 config files | ~5MB | All libraries, extensions, web files | Yes (for links) |
| **Optimized** | Config + main JARs | ~50MB | Library directories only | Yes (for links) |
| **Full Copy (Original)** | All files | ~500MB+ | None | No |

**Recommendation:** Use the Minimal approach to save maximum space while maintaining full functionality.

## Complete Switchover Process

### Phase 1: Copy Configuration Files

**Step 1:** Run the main configuration copy script:
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles"
.\Util_CopyConfigFiles.ps1
```

This copies all configuration files (*.properties, *.xml, *.json, etc.) while preserving directory structure.

### Phase 2: Set Up MirthConnect Runtime Environment

**Step 2:** Choose your MirthConnect setup approach:

**Option A: Minimal Setup (RECOMMENDED)**
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles\MirthConnect_SwitchOver"
.\MirthConnect_Minimal.ps1
```
This copies only essential configuration files and creates symbolic links for everything else, saving 500MB+ of space.

**Option B: Optimized Setup**
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles\MirthConnect_SwitchOver"
.\MirthConnect_SwitchOver.ps1
```
This creates symbolic links for library directories but copies main JARs and extensions.

### Phase 3: Modify VM Options Files

**Files Modified:**
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

**Change Applied:**
Added the following line to both files:
```
-Duser.dir=C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect
```

This system property tells the Java Virtual Machine to use the Config folder as the working directory, making MirthConnect load all files from that location.

## Required Files and Directories in Config Folder

The Config folder must contain all files that MirthConnect needs to operate. Here's the complete structure:

### Essential Directory Structure
```
Config\MirthConnect\CURR\Mirth Connect\
├── conf\                           # Configuration files
│   ├── mirth.properties            # Main configuration (CRITICAL)
│   ├── log4j2.properties          # Logging configuration (CRITICAL)
│   ├── dbdrivers.xml              # Database drivers
│   ├── mirth-cli-config.properties # CLI configuration
│   └── log4j2-cli.properties      # CLI logging
├── appdata\                        # Application data
│   ├── keystore.jks               # Security keystore (CRITICAL)
│   ├── server.id                  # Server identification
│   └── configuration.properties   # Runtime configuration
├── extensions\                     # MirthConnect extensions (CRITICAL)
│   ├── datatype-raw\              # Raw data type extension
│   │   ├── datatype-raw-client.jar
│   │   ├── datatype-raw-server.jar
│   │   ├── datatype-raw-shared.jar
│   │   └── plugin.xml
│   ├── datatype-hl7v2\            # HL7v2 data type extension
│   ├── datatype-xml\              # XML data type extension
│   ├── [... all other extensions] # All extension directories with JAR files
│   └── ...
├── cli-lib\                        # Command line interface libraries (CRITICAL)
├── client-lib\                     # Client libraries (CRITICAL)
├── manager-lib\                    # Manager libraries (CRITICAL)
├── server-lib\                     # Server libraries (CRITICAL)
├── docs\                           # Documentation
├── logs\                           # Log files (created at runtime)
├── webapps\                        # Web applications
│   └── webadmin.war               # Web admin interface (CRITICAL)
├── public_html\                    # Public web content
│   └── index.html                 # Default web page
├── public_api_html\                # API documentation
│   └── index.html                 # API documentation page
├── mirth-server-launcher.jar      # Main server JAR (CRITICAL)
├── mirth-cli-launcher.jar         # CLI launcher JAR
└── mirth-manager-launcher.jar     # Manager launcher JAR
```

### Critical Files for Operation

**Configuration Files (MUST be present):**
- `conf/mirth.properties` - Main configuration with database settings, ports, etc.
- `conf/log4j2.properties` - Logging configuration (updated to use relative paths)
- `appdata/keystore.jks` - Security certificates and keys
- `appdata/server.id` - Unique server identifier

**Runtime Libraries (MUST be present):**
- `cli-lib/` - All JAR files for command line interface
- `client-lib/` - All JAR files for client functionality
- `manager-lib/` - All JAR files for management functionality
- `server-lib/` - All JAR files for server core functionality

**Extension JAR Files (MUST be present):**
- All `*.jar` files in each extension subdirectory
- Without these, MirthConnect will fail with `ClassNotFoundException`

**Web Interface Files (MUST be present):**
- `webapps/webadmin.war` - The web administration interface
- `public_html/index.html` - Default web page
- `public_api_html/index.html` - API documentation page

**Main Application JARs (MUST be present):**
- `mirth-server-launcher.jar` - Core server application
- `mirth-cli-launcher.jar` - Command line interface
- `mirth-manager-launcher.jar` - Management interface

## Complete Setup Instructions

### Prerequisites
- MirthConnect must be installed and working from the original location
- PowerShell execution policy must allow script execution
- Administrative privileges for service operations

### Step 1: Copy Configuration Files

Run the main configuration copy script to copy all configuration files:

```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles"
.\Util_CopyConfigFiles.ps1
```

This copies all configuration files (*.properties, *.xml, *.json, etc.) while preserving directory structure.

### Step 2: Set Up MirthConnect Runtime Environment

Choose your preferred setup approach:

**Option A: Minimal Setup (RECOMMENDED for space efficiency)**
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles\MirthConnect_SwitchOver"
.\MirthConnect_Minimal.ps1
```

This script automatically:
- Copies ONLY essential configuration files (*.properties, keystore, etc.)
- Creates symbolic links for all library directories (saves ~400MB)
- Creates symbolic links for extensions directory (saves ~100MB)
- Creates symbolic links for web directories and main JARs
- Provides maximum space efficiency with minimal duplication

**Option B: Optimized Setup (fallback if symbolic links don't work)**
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles\MirthConnect_SwitchOver"
.\MirthConnect_SwitchOver.ps1
```

This script automatically:
- Creates symbolic links for library directories when possible
- Copies main JAR files and essential web files
- Falls back to copying if symbolic links fail

### Step 3: Verify VM Options Files

Ensure the VM options files have been updated with the working directory parameter:

**Check these files:**
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

**Verify this line is present:**
```
-Duser.dir=C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect
```

### Step 4: Stop Existing MirthConnect Service

Before applying changes, stop any running MirthConnect services:

```powershell
# Run as Administrator
Stop-Service -Name "MA_MirthConnect*" -Force
```

### Step 5: Create New Service (Optional)

If you want to create a new service with proper naming:

```powershell
cd "C:\Mobile Aspects\MirthConnect\CURR\Scripts\Mirth_CreateService"
.\Mirth_CreateService.ps1
```

This creates a service named `MA_MirthConnect_CURR` with automatic delayed start.

### Step 6: Start MirthConnect Service

Start the service to test the new configuration:

```powershell
# Run as Administrator
Start-Service -Name "MA_MirthConnect_CURR"
```

### Step 7: Verify Successful Switchover

Check that MirthConnect is running from the Config folder:

1. **Service Status:**
   ```powershell
   Get-Service -Name "MA_MirthConnect_CURR"
   ```

2. **Web Interface:**
   - Open browser to http://localhost:8081/
   - Should display MirthConnect login page

3. **Log Files:**
   - Check for new files in `Config\MirthConnect\CURR\Mirth Connect\logs\`
   - Verify no new entries in original logs location

## Verification

### Check Service Status
```powershell
Get-Service -Name "MA_MirthConnect"
```

### Check Log Files
Monitor the log files in the Config folder:
```
Config\MirthConnect\CURR\Mirth Connect\logs\mirth.log
```

### Access MirthConnect Administrator
The MirthConnect Administrator should connect to the same instance, now running from the Config folder.

## Benefits

1. **Centralized Configuration**: All configuration files are now in the Config folder
2. **Version Control**: Configuration changes can be tracked in the Config folder
3. **Backup and Restore**: Easier to backup and restore configurations
4. **Environment Management**: Different environments can have separate Config folders
5. **Deployment**: Configuration changes can be deployed independently of the application

## Troubleshooting

### Service Won't Start

1. **Check VM Options**: Verify the `-Duser.dir` parameter is correctly set in both `.vmoptions` files
2. **Check Permissions**: Ensure the service account has read/write access to the Config folder
3. **Check Paths**: Verify all paths in `mirth.properties` are accessible from the new working directory

### Configuration Not Loading

1. **Verify Working Directory**: Check that MirthConnect is actually using the Config folder as working directory
2. **Check File Paths**: Ensure all relative paths in configuration files are correct
3. **Check Extensions**: Verify that extensions are properly loaded from the Config folder

### Database Connection Issues

1. **Check Database Configuration**: Verify database settings in `Config\MirthConnect\CURR\Mirth Connect\conf\mirth.properties`
2. **Check Data Directory**: Ensure `dir.appdata` points to the correct location
3. **Check Permissions**: Verify database access permissions

## How to Switch Back to Original Folder

If you need to revert MirthConnect to use the original installation folder:

### Step 1: Stop MirthConnect Service
```powershell
# Run as Administrator
Stop-Service -Name "MA_MirthConnect_CURR" -Force
```

### Step 2: Remove VM Options Parameter

**Edit these files:**
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

**Remove this line from both files:**
```
-Duser.dir=C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect
```

### Step 3: Start MirthConnect Service
```powershell
# Run as Administrator
Start-Service -Name "MA_MirthConnect_CURR"
```

### Step 4: Verify Rollback
- Check that MirthConnect is accessible at http://localhost:8081/
- Verify that new log files appear in the original location:
  `MirthConnect\CURR\Mirth Connect\logs\`
- Confirm no new files are created in the Config folder

**Note:** After rollback, MirthConnect will use all configuration files from the original installation directory.

## Advanced Troubleshooting

### Missing Library Files Error

**Error:** `ClassNotFoundException` for MirthConnect core classes

**Cause:** Missing library directories (cli-lib, client-lib, manager-lib, server-lib)

**Solution:**
```powershell
# Copy all library directories using relative paths:
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\cli-lib" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\" -Recurse -Force
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\client-lib" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\" -Recurse -Force
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\manager-lib" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\" -Recurse -Force
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\server-lib" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\" -Recurse -Force
```

### Missing Extension JAR Files Error

**Error:** `ClassNotFoundException` for extension classes (e.g., `RawDataTypeProperties`)

**Cause:** Extension directories only contain XML files, missing JAR files

**Solution:**
```powershell
# Copy all extension directories with JAR files using relative paths:
Get-ChildItem "..\..\..\MirthConnect\CURR\Mirth Connect\extensions" -Directory | ForEach-Object {
    Copy-Item $_.FullName "..\..\..\Config\MirthConnect\CURR\Mirth Connect\extensions\" -Recurse -Force
}
```

### Web Interface Not Loading

**Error:** Port 8081 not responding, web interface inaccessible

**Cause:** Missing web application files

**Solution:**
```powershell
# Copy essential web files using relative paths:
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\webapps\webadmin.war" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\webapps\" -Force
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\public_html\index.html" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\public_html\" -Force
```

### Service Starts But Doesn't Function

**Symptoms:** Service shows as "Running" but MirthConnect doesn't work properly

**Cause:** Missing main JAR files

**Solution:**
```powershell
# Copy main JAR files using relative paths:
Copy-Item "..\..\..\MirthConnect\CURR\Mirth Connect\*.jar" "..\..\..\Config\MirthConnect\CURR\Mirth Connect\" -Force
```

## Complete File Checklist

Use this checklist to verify all required files are present in the Config folder:

### ✅ Configuration Files
- [ ] `conf/mirth.properties`
- [ ] `conf/log4j2.properties`
- [ ] `conf/dbdrivers.xml`
- [ ] `conf/mirth-cli-config.properties`
- [ ] `conf/log4j2-cli.properties`

### ✅ Application Data
- [ ] `appdata/keystore.jks`
- [ ] `appdata/server.id`
- [ ] `appdata/configuration.properties`

### ✅ Library Directories (with JAR files)
- [ ] `cli-lib/` (multiple JAR files)
- [ ] `client-lib/` (multiple JAR files)
- [ ] `manager-lib/` (multiple JAR files)
- [ ] `server-lib/` (multiple JAR files)

### ✅ Extension Directories (with JAR and XML files)
- [ ] `extensions/datatype-raw/` (3 JAR files + plugin.xml)
- [ ] `extensions/datatype-hl7v2/` (JAR files + plugin.xml)
- [ ] `extensions/datatype-xml/` (JAR files + plugin.xml)
- [ ] `extensions/[all other extensions]/` (JAR files + plugin.xml)

### ✅ Web Application Files
- [ ] `webapps/webadmin.war`
- [ ] `public_html/index.html`
- [ ] `public_api_html/index.html`

### ✅ Main Application JARs
- [ ] `mirth-server-launcher.jar`
- [ ] `mirth-cli-launcher.jar`
- [ ] `mirth-manager-launcher.jar`

### ✅ Runtime Directories
- [ ] `logs/` (created automatically)
- [ ] `docs/` (documentation files)

## Notes

- The original MirthConnect installation files remain unchanged
- Only the working directory is changed via the `-Duser.dir` JVM parameter
- All relative paths in configuration files will now be resolved relative to the Config folder
- This approach allows for easy rollback by simply removing the `-Duser.dir` parameter
- The setup script automates copying all required files to prevent manual errors

## File Locations

### Original Installation
```
C:\Mobile Aspects\MirthConnect\CURR\Mirth Connect\
```

### New Configuration Location
```
C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect\
```

### Modified Files
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

### Script Location
- `Script\Util_CopyConfigFiles\MirthConnect_SwitchOver\MirthConnect_SwitchOver.ps1`
- `Script\Util_CopyConfigFiles\MirthConnect_SwitchOver\README.md`

## Final Notes

- The executable files (mcservice.exe, mcserver.exe, etc.) remain in the original installation directory
- Only configuration files, extensions, and data are loaded from the Config folder
- This approach maintains the original installation while centralizing configuration management
- The change affects both service and standalone execution modes
- Use relative paths in all manual operations for better portability
