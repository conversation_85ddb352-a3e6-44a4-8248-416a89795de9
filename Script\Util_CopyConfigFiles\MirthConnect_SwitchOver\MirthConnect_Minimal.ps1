# PowerShell script to set up MINIMAL MirthConnect configuration with maximum space efficiency
# Author: Augment Agent
# Date: 2025-07-05
# This script copies ONLY the essential configuration files and creates symbolic links for everything else

param(
    [string]$ConfigRoot = "..\..\..\Config\MirthConnect\CURR\Mirth Connect",
    [string]$SourceRoot = "..\..\..\MirthConnect\CURR\Mirth Connect"
)

Write-Host "Setting up MINIMAL MirthConnect configuration (maximum space efficiency)..." -ForegroundColor Green
Write-Host "Config Root: $ConfigRoot" -ForegroundColor Yellow
Write-Host "Source Root: $SourceRoot" -ForegroundColor Yellow

# Only copy the ABSOLUTE ESSENTIALS that must be in the Config folder
# Everything else will be accessed via symbolic links to the original installation

Write-Host "`nCopying ONLY essential configuration files..." -ForegroundColor Cyan

# Essential files that MUST be copied (these contain environment-specific settings)
$EssentialFiles = @(
    "conf\mirth.properties",
    "conf\log4j2.properties", 
    "conf\dbdrivers.xml",
    "conf\mirth-cli-config.properties",
    "conf\log4j2-cli.properties",
    "appdata\keystore.jks",
    "appdata\server.id",
    "mcserver.vmoptions",
    "mcservice.vmoptions"
)

# Create necessary directories
$RequiredDirs = @("conf", "appdata", "logs")
foreach ($dir in $RequiredDirs) {
    $dirPath = Join-Path $ConfigRoot $dir
    if (!(Test-Path $dirPath)) {
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# Copy only the essential configuration files
foreach ($file in $EssentialFiles) {
    $sourcePath = Join-Path $SourceRoot $file
    $destPath = Join-Path $ConfigRoot $file

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        $destDir = Split-Path $destPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "Copied essential file: $file" -ForegroundColor Green
    } elseif (Test-Path $destPath) {
        Write-Host "File already exists: $file" -ForegroundColor Gray
    } else {
        Write-Host "Source file not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "`nCreating symbolic links for all other directories..." -ForegroundColor Cyan

# Create symbolic links for all the large directories to avoid duplication
$DirectoriesToLink = @(
    "cli-lib",
    "client-lib", 
    "manager-lib",
    "server-lib",
    "extensions",
    "webapps",
    "public_html",
    "public_api_html",
    "docs"
)

foreach ($dir in $DirectoriesToLink) {
    $sourcePath = Join-Path $SourceRoot $dir
    $destPath = Join-Path $ConfigRoot $dir

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        try {
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "Created symbolic link: $dir" -ForegroundColor Green
        } catch {
            Write-Host "Symbolic link failed for $dir (admin rights required)" -ForegroundColor Red
            Write-Host "  You can run this script as Administrator to create symbolic links" -ForegroundColor Yellow
            Write-Host "  Or manually copy the directory if needed" -ForegroundColor Yellow
        }
    } elseif (Test-Path $destPath) {
        Write-Host "Directory already exists: $dir" -ForegroundColor Gray
    } else {
        Write-Host "Source directory not found: $dir" -ForegroundColor Yellow
    }
}

Write-Host "`nCreating symbolic links for main JAR files..." -ForegroundColor Cyan

# Create symbolic links for main JAR files
$MainJars = @(
    "mirth-server-launcher.jar",
    "mirth-cli-launcher.jar", 
    "mirth-manager-launcher.jar"
)

foreach ($jar in $MainJars) {
    $sourcePath = Join-Path $SourceRoot $jar
    $destPath = Join-Path $ConfigRoot $jar

    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        try {
            New-Item -ItemType SymbolicLink -Path $destPath -Target $sourcePath -Force | Out-Null
            Write-Host "Created symbolic link for JAR: $jar" -ForegroundColor Green
        } catch {
            Write-Host "Symbolic link failed for $jar, copying instead..." -ForegroundColor Yellow
            Copy-Item -Path $sourcePath -Destination $destPath -Force
            Write-Host "Copied JAR file: $jar" -ForegroundColor Green
        }
    } elseif (Test-Path $destPath) {
        Write-Host "JAR file already exists: $jar" -ForegroundColor Gray
    } else {
        Write-Host "Source JAR not found: $jar" -ForegroundColor Yellow
    }
}

Write-Host "`nMINIMAL MirthConnect setup completed!" -ForegroundColor Green
Write-Host "MAXIMUM SPACE EFFICIENCY: Only essential config files copied, everything else linked" -ForegroundColor Magenta
Write-Host "Estimated space saved: ~500MB+ (hundreds of JAR files not duplicated)" -ForegroundColor Magenta
Write-Host "`nConfiguration location: $ConfigRoot" -ForegroundColor Yellow
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Update VM options files with -Duser.dir parameter pointing to Config folder" -ForegroundColor White
Write-Host "2. Restart MirthConnect service" -ForegroundColor White
Write-Host "3. Verify web interface at http://localhost:8081/" -ForegroundColor White
Write-Host "`nNote: If symbolic links failed, run as Administrator or use the full copy script" -ForegroundColor Yellow
